<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('添加机构人员')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: bootstrap-tagsinput-css" />
    <style type="text/css">
        .bootstrap-tagsinput {
            width: 100%;
        }
        .label-info {
            background-color: #5bc0de;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #1ab394;
            padding-bottom: 5px;
            margin: 20px 0 15px 0;
        }
        .section-title:first-child {
            margin-top: 0;
        }
        .form-section {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .need-field {
            color: #6900a1;
        }
    </style>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-staff-add">
        <input name="orgCode" type="hidden" th:value="${orgCode}">
        <!-- 简介信息部分 -->
        <div class="section-title">简介信息</div>
        <div class="form-section">
            <div class="form-group">
                <label class="col-sm-1 control-label is-required need-field" style="text-wrap: nowrap;">英文简介：</label>
                <div class="col-sm-10">
                    <textarea name="profileEn" id="profileEn" class="form-control" rows="4" placeholder="请输入英文简介，AI将根据此内容自动解析人员信息" required></textarea>
                </div>
                <div class="col-sm-1">
                    <button type="button" class="btn btn-info btn-sm" onclick="parseEnglishBio()" id="parseBtn" style="margin-top: 10px;">
                        <i class="fa fa-magic"></i> AI解析
                    </button>
                    <div id="parseLoading" style="display: none; margin-top: 10px;">
                        <i class="fa fa-spinner fa-spin"></i> <span id="parseStatus">AI解析中...</span>
                    </div>
                </div>
            </div>



            <div class="form-group">
                <label class="col-sm-1 control-label is-required need-field" style="text-wrap: nowrap;">数据来源：</label>
                <div class="col-sm-11">
                    <input name="source" class="form-control" type="text" placeholder="请输入数据来源URL或描述" required>
                </div>
            </div>
        </div>

        <!-- 基本信息部分 -->
        <div class="section-title">基本信息</div>
        <div class="form-section">
            <div class="row">
                <div class="col-sm-9">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required need-field">国家/地区：</label>
                                <div class="col-sm-8">
                                    <select name="country" id="country" class="form-control" th:with="type=${@dict.getType('sys_country')}" required>
                                        <option value="" style="color: #b6b6b6" disabled selected>选择国家/地区</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required need-field">中文名称：</label>
                                <div class="col-sm-8">
                                    <input name="peopleNameCn" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label need-field">英文名称：</label>
                                <div class="col-sm-8">
                                    <input name="peopleNameEn" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required need-field">职位：</label>
                                <div class="col-sm-8">
                                    <input name="position" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">状态：</label>
                                <div class="col-sm-8">
                                    <select name="status" class="form-control" th:with="type=${@dict.getType('sys_staff_status')}" required>
                                        <option value="" style="color: #b6b6b6" disabled>选择状态</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue == 'current'}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">人员编码：</label>
                                <div class="col-sm-8">
                                    <input name="peopleCode" id="peopleCode" class="form-control" type="text" readonly="readonly">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group text-center">
                        <input type="hidden" name="avatar" id="avatar">
                        <p class="user-info-head" onclick="peopleAvatar()">
                            <img class="img-lg" id="avatarUrl" th:src="@{/img/default_people.png}" th:onerror="'this.src=\'' + @{'/img/default_people.png'} + '\''">
                        </p>
                        <p><input type="file" id="peopleAvatarInput" style="display: none;"><a onclick="peopleAvatar()">点击上传头像</a></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细信息部分 -->
        <div class="section-title">详细信息</div>
        <div class="form-section">
            <div class="row">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">性别：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('sys_user_sex')}">
                                <input type="radio" th:id="${'gender_' + dict.dictCode}" name="gender" th:value="${dict.dictValue}" th:checked="${dict.default}">
                                <label th:for="${'gender_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">出生地：</label>
                        <div class="col-sm-8">
                            <input name="birthplace" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">人员所属类型：</label>
                        <div class="col-sm-8">
                            <select name="peopleTypeDetail" id="peopleTypeDetail" class="form-control" th:with="type=${@dict.getType('personnel_officer_type')}">
                                <option value="" style="color: #b6b6b6" disabled selected>选择人员所属类型</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">所属政党：</label>
                        <div class="col-sm-8">
                            <input name="party" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">所属军兵种：</label>
                        <div class="col-sm-8">
                            <select name="troopsCategory" class="form-control" th:with="type=${@dict.getType('sys_troops_categories')}">
                                <option value="" style="color: #b6b6b6" disabled selected>选择所属军兵种</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">军衔：</label>
                        <div class="col-sm-8">
                            <input name="militaryRank" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">职业：</label>
                        <div class="col-sm-8">
                            <input name="occupation" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">所在机构：</label>
                        <div class="col-sm-8">
                            <input name="orgName" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">当前职务：</label>
                        <div class="col-sm-8">
                            <input name="post" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-8">
                    <div class="form-group">
                        <label class="col-sm-2 control-label need-field">任职日期：</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-4">
                                    <select name="appointmentYear" id="appointmentYear" class="form-control">
                                        <option value="">年份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="appointmentMonth" id="appointmentMonth" class="form-control" disabled="disabled">
                                        <option value="">月份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="appointmentDay" id="appointmentDay" class="form-control" disabled="disabled">
                                        <option value="">日</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">工作状态：</label>
                        <div class="col-sm-8">
                            <select name="workStatus" class="form-control">
                                <option value="">选择工作状态</option>
                                <option value="current" selected>在职</option>
                                <option value="former">离职</option>
                                <option value="retired">退休</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-8">
                    <div class="form-group">
                        <label class="col-sm-2 control-label need-field">出生日期：</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-4">
                                    <select name="birthdayYear" id="birthdayYear" class="form-control">
                                        <option value="">年份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="birthdayMonth" id="birthdayMonth" class="form-control" disabled="disabled">
                                        <option value="">月份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="birthdayDay" id="birthdayDay" class="form-control" disabled="disabled">
                                        <option value="">日</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">年龄：</label>
                        <div class="col-sm-8">
                            <input name="age" id="age" class="form-control" type="text" number="true">
                        </div>
                    </div>
                </div>

            </div>
            <div class="row">
                <div class="col-sm-8">
                    <div class="form-group">
                        <label class="col-sm-2 control-label need-field">毕业院校：</label>
                        <div class="col-sm-10">
                            <input name="graduatedUniversity" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">学历：</label>
                        <div class="col-sm-8">
                            <input name="education" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">

                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">工作地点：</label>
                        <div class="col-sm-8">
                            <input name="workplace" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">邮箱：</label>
                        <div class="col-sm-8">
                            <input name="email" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4 control-label need-field">联系方式：</label>
                        <div class="col-sm-8">
                            <input name="telephone" class="form-control" type="text">
                        </div>
                    </div>
                </div>

            </div>
        </div>


        <!-- 经历信息部分 -->
        <div class="section-title">经历信息</div>
        <div class="form-section">
            <div class="form-group">
                <label class="col-sm-1 control-label need-field">中文简介：</label>
                <div class="col-sm-11">
                    <textarea name="profileCn" class="form-control" rows="4" placeholder="AI解析后会自动填充中文简介，也可手动输入"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label need-field">教育经历：</label>
                <div class="col-sm-11">
                    <textarea name="educationalExperience" class="form-control" rows="4" placeholder="请输入教育经历，每条记录一行"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label need-field">工作经历：</label>
                <div class="col-sm-11">
                    <textarea name="assignments" class="form-control" rows="4" placeholder="请输入工作经历，每条记录一行"></textarea>
                </div>
            </div>
        </div>


    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>确 定</button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer" />
<th:block th:include="include :: select2-js" />
<th:block th:include="include :: jasny-bootstrap-js" />
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-tagsinput-js" />

<script th:inline="javascript">
    var prefix = ctx + "system/architecture/personnel";

    $("#form-staff-add").validate({
        focusCleanup: true
    });

    $(function() {
        // 初始化日期选择器
        initDateSelectors();

        // 初始化任职日期选择器
        initAppointmentDateSelectors();

        // 获取人员编码
        generatePeopleCode();
    });

    // 初始化日期选择器
    function initDateSelectors() {
        var birthdayYear = document.getElementById("birthdayYear");
        var date = new Date();
        var year = date.getFullYear();

        // 组建年份选择器
        for (var i = year; i >= year - 110; i--) {
            birthdayYear.options.add(new Option(i, i));
        }

        // 组建月份选择器
        var birthdayMonth = document.getElementById("birthdayMonth");
        for (var j = 1; j <= 12; j++) {
            if (j < 10) {
                birthdayMonth.options.add(new Option('0' + j, '0' + j));
            } else {
                birthdayMonth.options.add(new Option(j, j));
            }
        }

        // 组建日选择器
        let birthdayDay = document.getElementById("birthdayDay");
        for (let j = 1; j <= 31; j++) {
            if (j < 10) {
                birthdayDay.options.add(new Option('0' + j, '0' + j));
            } else {
                birthdayDay.options.add(new Option(j, j));
            }
        }

        // 年份变化事件
        $("#birthdayYear").change(function() {
            let birthday = $('#birthdayYear').select2('val');
            $('#age').val(year - birthday); // 计算年龄
            var birthdayYear = $('#birthdayYear option:selected').val();
            if (birthdayYear != '' && birthdayYear != null) {
                $('#birthdayMonth').attr("disabled", false);
            } else {
                $('#birthdayMonth').attr("disabled", true);
                $("#birthdayMonth").select2("val", [""]);
            }
        });

        // 月份变化事件
        $("#birthdayMonth").change(function() {
            let birthdayMonth = $('#birthdayMonth option:selected').val();
            if (birthdayMonth != '' && birthdayMonth != null) {
                $('#birthdayDay').attr("disabled", false);
            } else {
                $("#birthdayDay").select2("val", [""]);
                $('#birthdayDay').attr("disabled", true);
            }
        });
    }

    // 初始化任职日期选择器
    function initAppointmentDateSelectors() {
        var appointmentYear = document.getElementById("appointmentYear");
        var date = new Date();
        var year = date.getFullYear();

        // 组建年份选择器（从当前年份到过去50年）
        for (var i = year; i >= year - 50; i--) {
            appointmentYear.options.add(new Option(i, i));
        }

        // 组建月份选择器
        var appointmentMonth = document.getElementById("appointmentMonth");
        for (var j = 1; j <= 12; j++) {
            if (j < 10) {
                appointmentMonth.options.add(new Option('0' + j, '0' + j));
            } else {
                appointmentMonth.options.add(new Option(j, j));
            }
        }

        // 组建日选择器
        let appointmentDay = document.getElementById("appointmentDay");
        for (let j = 1; j <= 31; j++) {
            if (j < 10) {
                appointmentDay.options.add(new Option('0' + j, '0' + j));
            } else {
                appointmentDay.options.add(new Option(j, j));
            }
        }

        // 年份变化事件
        $("#appointmentYear").change(function() {
            var appointmentYear = $('#appointmentYear option:selected').val();
            if (appointmentYear != '' && appointmentYear != null) {
                $('#appointmentMonth').attr("disabled", false);
            } else {
                $('#appointmentMonth').attr("disabled", true);
                $("#appointmentMonth").select2("val", [""]);
            }
        });

        // 月份变化事件
        $("#appointmentMonth").change(function() {
            let appointmentMonth = $('#appointmentMonth option:selected').val();
            if (appointmentMonth != '' && appointmentMonth != null) {
                $('#appointmentDay').attr("disabled", false);
            } else {
                $("#appointmentDay").select2("val", [""]);
                $('#appointmentDay').attr("disabled", true);
            }
        });
    }

    // 生成人员编码
    function generatePeopleCode() {
        $.ajax({
            cache: true,
            type: "POST",
            url: prefix + "/selectMaxPeopleCode",
            async: false,
            error: function(request) {
                $.modal.alertError("系统错误");
            },
            success: function(maxPeopleCode) {
                document.getElementById("peopleCode").value = maxPeopleCode;
            }
        });
    }

    // 提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var peopleCode = document.getElementById("peopleCode").value;
            if (peopleCode && peopleCode.trim() !== '') {
                // 如果有人员编码，检查是否重复
                $.get(prefix + "/checkoutPeopleCode/" + peopleCode, function(result) {
                    if (!result) {
                        $.modal.alertError('人员编码重复');
                    } else {
                        submitForm();
                    }
                }).fail(function() {
                    // 如果检查接口失败，直接提交
                    submitForm();
                });
            } else {
                // 如果没有人员编码，直接提交（让后端生成）
                submitForm();
            }
        }
    }

    // 实际提交表单的函数
    function submitForm() {
        $.ajax({
            type: "POST",
            url: prefix + "/addStaff",
            data: $('#form-staff-add').serialize(),
            dataType: 'json',
            beforeSend: function() {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function(result) {
                $.modal.closeLoading();
                if (result.code == 0 || result.code == 200) {
                    $.modal.msgSuccess(result.msg || "操作成功");
                    // 关闭当前页面
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                    // 刷新父页面的表格
                    if (parent.$.table && parent.$.table.refresh) {
                        parent.$.table.refresh();
                    }
                } else {
                    $.modal.alertError(result.msg || "操作失败");
                }
            },
            error: function(xhr, status, error) {
                $.modal.closeLoading();
                console.error('提交失败:', error);
                $.modal.alertError("提交失败，请检查网络连接");
            }
        });
    }

    // 上传人员头像
    function peopleAvatar() {
        $('#peopleAvatarInput').trigger('click');
    }

    $("#peopleAvatarInput").change(function() {
        var data = new FormData();
        data.append("file", $("#peopleAvatarInput")[0].files[0]);
        $.ajax({
            type: "POST",
            url: ctx + "common/upload/img",
            data: data,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(result) {
                console.log('头像上传结果:', result);
                if (result.code == 0 || result.code == 200 || result.code == '0' || result.code == '200') {
                    $("#avatarUrl").attr("src", result.url);
                    $("#avatar").val(result.url);
                    $.modal.msgSuccess("头像上传成功");
                } else {
                    $.modal.alertError("头像上传失败：" + (result.msg || "未知错误"));
                }
            },
            error: function(xhr, status, error) {
                console.error('头像上传失败:', error);
                $.modal.alertError("头像上传失败，请检查网络连接");
            }
        });
    });

    // AI解析英文简介功能
    function parseEnglishBio() {
        var profileEn = $('#profileEn').val().trim();
        if (!profileEn) {
            $.modal.alertWarning('请先输入英文简介内容');
            return;
        }

        // 显示加载状态
        $('#parseBtn').hide();
        $('#parseLoading').show();
        $('#parseStatus').text('AI正在解析人员信息...');

        $.ajax({
            type: "POST",
            url: prefix + "/parseEnglishBio",
            data: {
                'profileEn': profileEn
            },
            dataType: 'json',
            timeout: 300000, // 5分钟超时
            success: function(result) {
                $('#parseBtn').show();
                $('#parseLoading').hide();

                if (result.code == 0) {
                    // 解析成功，填充表单数据
                    $('#parseStatus').text('正在填充表单数据...');
                    setTimeout(function() {
                        fillFormWithParsedData(result.data);
                        var message = 'AI解析完成，已自动填充相关字段';
                        if (result.data.profileCn) {
                            message += '（包含中文翻译）';
                        }
                        $.modal.msgSuccess(message);
                    }, 500);
                } else {
                    $.modal.alertError('AI解析失败：' + (result.msg || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                $('#parseBtn').show();
                $('#parseLoading').hide();

                var errorMsg = 'AI解析服务异常';
                if (status === 'timeout') {
                    errorMsg = 'AI解析超时（5分钟），请检查：\n1. 本地LLM服务是否正常运行\n2. 网络连接是否正常\n3. 服务地址配置是否正确';
                } else if (status === 'error') {
                    if (xhr.status === 0) {
                        errorMsg = 'AI服务连接失败，请检查：\n1. LLM服务地址：http://192.168.0.222:8000\n2. 服务是否启动\n3. 网络连接是否正常';
                    } else if (xhr.status === 404) {
                        errorMsg = 'AI服务接口不存在（404），请检查服务配置';
                    } else if (xhr.status === 500) {
                        errorMsg = 'AI服务内部错误（500），请检查服务日志';
                    } else {
                        errorMsg = 'AI服务返回错误（' + xhr.status + '）：' + (xhr.responseText || error);
                    }
                } else {
                    errorMsg = 'AI解析异常：' + error;
                }

                console.error('AI解析错误详情：', {
                    status: status,
                    error: error,
                    xhr: xhr
                });

                $.modal.alertError(errorMsg);
            }
        });
    }

    // 填充解析后的数据到表单
    function fillFormWithParsedData(data) {
        if (!data) return;

        // 填充基本信息
        if (data.nameEn) {
            $('input[name="peopleNameEn"]').val(data.nameEn);
        }
        if (data.nameCn) {
            $('input[name="peopleNameCn"]').val(data.nameCn);
        }
        if (data.militaryRank) {
            $('input[name="militaryRank"]').val(data.militaryRank);
        }
        if (data.post) {
            $('input[name="post"]').val(data.post);
            $('input[name="position"]').val(data.post); // 同时填充职位
        }
        if (data.orgName) {
            $('input[name="orgName"]').val(data.orgName);
        }
        if (data.birthplace) {
            $('input[name="birthplace"]').val(data.birthplace);
        }
        if (data.graduatedUniversity) {
            $('input[name="graduatedUniversity"]').val(data.graduatedUniversity);
        }
        if (data.education) {
            $('input[name="education"]').val(data.education);
        }

        // 填充翻译后的中文简介
        if (data.profileCn) {
            $('textarea[name="profileCn"]').val(data.profileCn);
        }

        // 填充经历信息
        if (data.educationalExperiences) {
            $('textarea[name="educationalExperience"]').val(data.educationalExperiences);
        }
        if (data.assignments) {
            $('textarea[name="assignments"]').val(data.assignments);
        }

        // 填充任职日期
        if (data.appointmentYear) {
            setSelect2Value('#appointmentYear', data.appointmentYear);
            $('#appointmentYear').trigger('change');
            if (data.appointmentMonth) {
                setTimeout(function() {
                    setSelect2Value('#appointmentMonth', data.appointmentMonth);
                    $('#appointmentMonth').trigger('change');
                    if (data.appointmentDay) {
                        setTimeout(function() {
                            setSelect2Value('#appointmentDay', data.appointmentDay);
                        }, 300);
                    }
                }, 300);
            }
        }

        // 填充工作状态
        if (data.workStatus) {
            $('select[name="workStatus"]').val(data.workStatus);
        }

        // 处理出生日期
        if (data.birthdayYear) {
            setSelect2Value('#birthdayYear', data.birthdayYear);
            $('#birthdayYear').trigger('change');
            if (data.birthdayMonth) {
                setTimeout(function() {
                    setSelect2Value('#birthdayMonth', data.birthdayMonth);
                    $('#birthdayMonth').trigger('change');
                    if (data.birthdayDay) {
                        setTimeout(function() {
                            setSelect2Value('#birthdayDay', data.birthdayDay);
                        }, 300);
                    }
                }, 300);
            }
        }

        // 处理性别
        if (data.gender) {
            setTimeout(function() {
                $('input[name="gender"]').prop('checked', false);
                var genderRadio = $('input[name="gender"][value="' + data.gender + '"]');
                if (genderRadio.length > 0) {
                    genderRadio.prop('checked', true);
                    genderRadio.trigger('change');
                }
            }, 200);
        }

        // 处理国家
        if (data.country) {
            setTimeout(function() {
                setSelect2Value('#country', data.country);
            }, 100);
        }

        // 处理军兵种
        if (data.troopsCategory) {
            setTimeout(function() {
                setSelect2Value('select[name="troopsCategory"]', data.troopsCategory);
            }, 100);
        }

        // 处理人员所属类型
        if (data.peopleType) {
            setTimeout(function() {
                setSelect2Value('#peopleTypeDetail', data.peopleType);
            }, 100);
        }
    }

    // Select2组件设置值的辅助函数
    function setSelect2Value(selector, value) {
        var $element = $(selector);
        if ($element.length > 0) {
            try {
                if ($element.hasClass('select2-hidden-accessible')) {
                    $element.select2("val", [value]);
                } else {
                    $element.val(value);
                }
                $element.trigger('change');
            } catch (e) {
                $element.val(value).trigger('change');
            }
        }
    }
</script>
</body>
</html>
