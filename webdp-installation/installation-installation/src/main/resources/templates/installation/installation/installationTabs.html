<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改军事基地Tabs')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .kv-avatar .krajee-default.file-preview-frame,.kv-avatar .krajee-default.file-preview-frame:hover {
        margin: 0;
        padding: 0;
        border: none;
        box-shadow: none;
        text-align: center;
    }
    .kv-avatar {
        display: inline-block;
    }
    .kv-avatar .file-input {
        display: table-cell;
        width: 213px;
    }
    .kv-reqd {
        color: red;
        font-family: monospace;
        font-weight: normal;
    }
</style>
<th:block th:include="include :: footer" />
<th:block th:include="include :: datetimepicker-js" />
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-table-resizable-js" />
<body class="white-bg">

<div class="row">
    <div class="col-sm-12">
        <div class="tabs-container">
            <ul class="nav nav-tabs">
                <li class="active"><a data-toggle="tab" href="#installationTab" aria-expanded="true"> 基本信息</a></li>
                <li class=""><a data-toggle="tab" href="#installationStaffTab" aria-expanded="false">指挥官</a></li>
                <li class=""><a data-toggle="tab" href="#installationHistoryTab" aria-expanded="false">发展历程</a></li>
                <li class=""><a data-toggle="tab" href="#installationTroopsTab" aria-expanded="false">驻扎单位</a></li>
                <li class=""><a data-toggle="tab" href="#weaponTroopsTab" aria-expanded="false">武器装备</a></li>
                <li class=""><a data-toggle="tab" href="#installationFacilitiesTab" aria-expanded="false">主要设施</a></li>
                <li class=""><a data-toggle="tab" href="#installationNewsTab" aria-expanded="false">新闻报道</a></li>
                <li class=""><a data-toggle="tab" href="#installationMultimediaTab" aria-expanded="false">图片视频</a></li>
            </ul>
            <div class="tab-content" id="content-all">
                <div id="installationTab" class="tab-pane active"></div>
                <div id="installationStaffTab" class="tab-pane"></div>
                <div id="installationHistoryTab" class="tab-pane"></div>
                <div id="installationTroopsTab" class="tab-pane"></div>
                <div id="weaponTroopsTab" class="tab-pane"></div>
                <div id="installationFacilitiesTab" class="tab-pane"></div>
                <div id="installationNewsTab" class="tab-pane"></div>
                <div id="installationMultimediaTab" class="tab-pane"></div>
            </div>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var orgCode = encodeURI(encodeURI([[${installationCode}]]));
    var installationCode = encodeURI(encodeURI([[${installationCode}]]));

    let tabsData = [{
        "id" : "installationTab",
        "url" : "/installation/installation/installationEdit/" + installationCode
    },{
        "id" : "installationStaffTab",
        "url" : "/organization/staff/" + installationCode
    },{
        "id" : "installationHistoryTab",
        "url" : "/organization/history/" + installationCode
    },{
        "id" : "installationTroopsTab",
        "url" : "/installation/unit/" + installationCode
    },{
        "id" : "weaponTroopsTab",
        "url" : "/organization/weapon/" + installationCode
    },{
        "id" : "installationFacilitiesTab",
        "url" : "/installation/facilities/" + installationCode
    },{
        "id" : "installationNewsTab",
        "url" : "/organization/news/" + installationCode
    },{
        "id" : "installationMultimediaTab",
        "url" : "/multimedia/installation/" + installationCode
    }];

    $(tabsData).each(function(){
        $("a[href='#"+this.id+"']").bind('click',{
            id : this.id,
            url : this.url
        },tabsHandler);
    });
    function tabsHandler(event) {
        let data = event.data;
        showTabs(data.id,data.url);
        return false; //阻止默认a标签响应
    }

    /**
     * 激活tab选项卡并使用ajax异步加载内容
     * @param {Object} tabsId
     * @param {Object} url
     */
    function showTabs(tabsId,url) {
        $("a[href='#"+tabsId+"']").tab('show');
        let $tabContent = $('#'+tabsId);
        if($tabContent.html().length < 100) {
            $tabContent.load(url);
        }
    }

    $("a[href='#installationTab']").trigger("click");

</script>
</body>
</html>