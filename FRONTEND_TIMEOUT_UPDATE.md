# 前端AI解析超时时间调整

## 问题描述
后台AI服务调用正常，但在第二次调用AI服务器接口期间（翻译阶段），前端页面报服务器超时错误。原因是前端AJAX请求超时时间设置为2分钟，而AI解析包含两次调用（信息提取 + 中文翻译），总耗时超过了前端超时限制。

## 解决方案
将前端AJAX请求的超时时间从2分钟（120000ms）调整为5分钟（300000ms）。

## 修改文件列表

### 1. 组织架构-人员管理
- `webdp-organization/organization-staff/src/main/resources/templates/organization/staff/addStaff.html`

### 2. 系统架构-人员管理
- `webdp-system-architecture/src/main/resources/templates/system/architecture/personnel/addStaff.html`
- `webdp-system-architecture/src/main/resources/templates/system/architecture/personnel/editStaff.html`

### 3. 人员管理-军官管理
- `webdp-personnel/personnel-officer/src/main/resources/templates/personnel/officer/addOfficer.html`
- `webdp-personnel/personnel-officer/src/main/resources/templates/personnel/officer/editOfficer.html`

## 具体修改内容

### 超时时间调整
```javascript
// 修改前
timeout: 120000, // 2分钟超时

// 修改后
timeout: 300000, // 5分钟超时
```

### 错误提示信息更新
```javascript
// 修改前
errorMsg = 'AI解析超时（2分钟），请检查：\n1. 本地LLM服务是否正常运行\n2. 网络连接是否正常\n3. 服务地址配置是否正确';

// 修改后
errorMsg = 'AI解析超时（5分钟），请检查：\n1. 本地LLM服务是否正常运行\n2. 网络连接是否正常\n3. 服务地址配置是否正确';
```

## AI解析流程说明

AI解析功能包含两个主要步骤：

1. **信息提取阶段**（第一次AI调用）
   - 从英文简介中提取人员基本信息
   - 包括姓名、职务、军衔、机构等结构化数据

2. **翻译阶段**（第二次AI调用）
   - 将英文简介翻译为中文
   - 生成中文简介内容

由于需要两次AI调用，总耗时可能超过2分钟，特别是在以下情况下：
- 简介内容较长
- AI模型响应较慢
- 网络延迟较高

## 预期效果

调整超时时间后：
- ✅ 避免在AI解析过程中出现前端超时错误
- ✅ 用户可以正常完成完整的AI解析流程
- ✅ 提升用户体验，减少操作中断

## 注意事项

1. **用户体验**：5分钟的等待时间较长，建议在界面上提供清晰的进度提示
2. **网络环境**：在网络较慢的环境下，可能仍需要更长的超时时间
3. **服务器性能**：如果AI服务器性能较低，可能需要进一步优化模型配置

## 后续优化建议

1. **进度提示优化**：
   - 显示当前处理阶段（信息提取/翻译）
   - 提供更详细的进度反馈

2. **性能优化**：
   - 考虑将信息提取和翻译合并为一次AI调用
   - 优化AI模型参数以提高响应速度

3. **用户体验**：
   - 添加取消功能，允许用户中断长时间的解析过程
   - 提供解析结果的部分保存功能

## 测试验证

部署后请验证以下场景：
1. 短文本AI解析（应在1-2分钟内完成）
2. 长文本AI解析（可能需要3-4分钟）
3. 网络较慢环境下的AI解析
4. 超时错误提示是否正确显示新的时间（5分钟）

## 部署说明

1. 无需重启后端服务，仅前端页面修改
2. 清除浏览器缓存以确保加载最新的JavaScript代码
3. 建议在测试环境先验证功能正常后再部署到生产环境
