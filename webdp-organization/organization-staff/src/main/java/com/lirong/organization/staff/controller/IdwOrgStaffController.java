package com.lirong.organization.staff.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.service.IdwOrgService;
import com.lirong.organization.staff.domain.StaffRelevance;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.common.utils.StringUtils;
import com.lirong.organization.staff.domain.IdwOrgStaff;
import com.lirong.organization.staff.service.IdwOrgStaffService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 机构人员Controller
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Controller
@RequestMapping("/organization/staff")
public class IdwOrgStaffController extends BaseController {
    public static final Logger log = LoggerFactory.getLogger(IdwOrgStaffController.class);

    private String prefix = "organization/staff";

    private final String SHEET_NAME = "主要人员";

    @Autowired//机构人员
    private IdwOrgStaffService idwOrgStaffService;
    @Autowired//组织机构
    private IdwOrgService idwOrgService;
    @Autowired//人员信息
    private IdwPeopleMainService idwPeopleMainService;

    /**
     * 跳转人员关联列表
     *
     * @return 结果
     */
    @RequiresPermissions("organization:staff:view")
    @GetMapping("/relevance")
    public String staffRelevance() {
        return prefix + "/relevance/relevance";
    }

    /**
     * 查询没有关联的机构人员数据
     *
     * @param staffRelevance 查询参数
     * @return 结果
     */
    @RequiresPermissions("organization:staff:list")
    @PostMapping("/notRelevance")
    @ResponseBody
    public TableDataInfo notRelevance(StaffRelevance staffRelevance) {
        startPage();
        List<StaffRelevance> list = idwOrgStaffService.selectNotRelevanceOrgStaff(staffRelevance);
        return getDataTable(list);
    }

    /**
     * 机构人员关联人员
     *
     * @param staffIds   机构人员ID
     * @param peopleCode 人员编码
     * @return 结果
     */
    @RequiresPermissions("organization:staff:edit")
    @PostMapping("/relevancePeople")
    @ResponseBody
    public AjaxResult relevancePeople(String staffIds, String peopleCode) {
        return toAjax(idwOrgStaffService.relevancePeople(staffIds, peopleCode));
    }

    /**
     * 机构人员关联所有匹配人员
     *
     * @return 结果
     */
    @RequiresPermissions("organization:staff:edit")
    @GetMapping("/relevancePeopleAll")
    @ResponseBody
    public AjaxResult relevancePeopleAll() {
        return toAjax(idwOrgStaffService.relevancePeopleAll());
    }

    /**
     * 跳转机构人员列表
     *
     * @param orgCode 机构编码
     * @param mmap    返回参数
     * @return 结果
     */
    @RequiresPermissions("organization:staff:view")
    @GetMapping("/{orgCode}")
    public String staffList(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("机构人员跳转列表页解析参数失败！");
        }
        mmap.put("orgCode", orgCode);
        return prefix + "/staffList";
    }

    /**
     * 查询机构人员列表
     */
    @RequiresPermissions("organization:staff:list")
    @PostMapping("/staffList")
    @ResponseBody
    public TableDataInfo staffList(IdwOrgStaff idwOrgStaff) {
        startPage();
        List<IdwOrgStaff> list = idwOrgStaffService.selectIdwOrgStaffList(idwOrgStaff);
        return getDataTable(list);
    }

    /**
     * 新增机构人员
     */
    @GetMapping("/addStaff/{orgCode}")
    public String addStaff(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("新增机构人员跳转页面,解析机构编码失败！");
        }
        List<IdwOrgStaff> orgStaffList = idwOrgStaffService.selectOrgStaffByOrgCode(orgCode);
        IdwOrg idwOrg = idwOrgService.selectOrgByOrgCode(orgCode);
        if (null != idwOrg) {
            mmap.put("orgType", idwOrg.getOrgType());
        }
        mmap.put("orgCode", orgCode);
        mmap.put("orgStaffList", orgStaffList);
        return prefix + "/addStaff";
    }

    /**
     * 新增机构人员
     */
    @GetMapping("/addStaffWithAi/{orgCode}")
    public String addStaffWithAi(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("新增机构人员跳转页面,解析机构编码失败！");
        }
        List<IdwOrgStaff> orgStaffList = idwOrgStaffService.selectOrgStaffByOrgCode(orgCode);
        IdwOrg idwOrg = idwOrgService.selectOrgByOrgCode(orgCode);
        if (null != idwOrg) {
            mmap.put("orgType", idwOrg.getOrgType());
        }
        mmap.put("orgCode", orgCode);
        mmap.put("orgStaffList", orgStaffList);
        return prefix + "/addStaffWithAI";
    }

    /**
     * 新增保存机构人员（融合机构人员和详细人员信息）
     */
    @RequiresPermissions("organization:staff:add")
    @Log(title = "机构人员", businessType = BusinessType.INSERT)
    @PostMapping("/addStaff")
    @ResponseBody
    public AjaxResult addStaffSave(IdwOrgStaff staff, HttpServletRequest request) {
        try {
            // 从请求中获取详细人员信息的参数
            IdwPeopleMain peopleMain = extractPeopleMainFromRequest(request);

            // 先保存详细人员信息（如果有的话）
            if (peopleMain != null && StringUtils.isNotEmpty(peopleMain.getNameCn())) {
                // 生成人员编码
                String maxPeopleCode = idwPeopleMainService.selectMaxPeopleCode();
                String newPeopleCodeNumber = "0000000" + String.valueOf(Long.parseLong(maxPeopleCode.substring(1)) + 1);
                String peopleCode = maxPeopleCode.charAt(0) + newPeopleCodeNumber.substring(newPeopleCodeNumber.length() - 7);

                peopleMain.setPeopleCode(peopleCode);
                peopleMain.setStatus("current"); // 设置默认状态
                peopleMain.setCategory("政府军队官员");
                peopleMain.setIsDelete(0);

                int result = idwPeopleMainService.insertIdwPeopleMain(peopleMain);

                // 关联到机构人员
                staff.setPeopleCode(peopleCode);
            }

            // 保存机构人员信息
            return toAjax(idwOrgStaffService.insertIdwOrgStaff(staff));
        } catch (Exception e) {
            log.error("新增人员失败", e);
            return AjaxResult.error("新增人员失败：" + e.getMessage());
        }
    }

    /**
     * 修改机构人员
     */
    @GetMapping("/editStaff/{staffId}")
    public String editStaff(@PathVariable("staffId") Long staffId, ModelMap mmap) {
        IdwOrgStaff idwOrgStaff = idwOrgStaffService.selectIdwOrgStaffById(staffId);
        List<IdwOrgStaff> orgStaffList = idwOrgStaffService.selectByOrgCodeAndExcludeStaffId(idwOrgStaff.getOrgCode(), idwOrgStaff.getStaffId());
        IdwOrg idwOrg = idwOrgService.selectOrgByOrgCode(idwOrgStaff.getOrgCode());
        if (null != idwOrg) {
            mmap.put("orgType", idwOrg.getOrgType());
        } else {

        }
        mmap.put("idwOrgStaff", idwOrgStaff);
        mmap.put("orgStaffList", orgStaffList);
        mmap.put("managerStaffId", idwOrgStaff.getManagerStaffId());
        return prefix + "/editStaff";
    }

    /**
     * 修改保存机构人员
     */
    @RequiresPermissions("organization:staff:edit")
    @Log(title = "机构人员", businessType = BusinessType.UPDATE)
    @PostMapping("/editStaff")
    @ResponseBody
    public AjaxResult editStaffSave(IdwOrgStaff idwOrgStaff) {
        return toAjax(idwOrgStaffService.updateIdwOrgStaff(idwOrgStaff));
    }

    /**
     * 删除机构人员
     */
    @RequiresPermissions("organization:staff:remove")
    @Log(title = "机构人员", businessType = BusinessType.DELETE)
    @PostMapping("/removeStaff")
    @ResponseBody
    public AjaxResult removeStaff(String ids) {
        return toAjax(idwOrgStaffService.deleteIdwOrgStaffByIds(ids));
    }

    /**
     * 导入下载模板
     *
     * @return 结果
     */
    @RequiresPermissions("organization:staff:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<IdwOrgStaff> util = new ExcelUtil<IdwOrgStaff>(IdwOrgStaff.class);
        return util.importTemplateExcel(SHEET_NAME);
    }

    /**
     * 导入
     *
     * @param file          机构人员信息数据列表
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @return 结果
     * @throws Exception
     */
    /*@Log(title = "机构人员信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions("organization:staff:import")
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<IdwOrgStaff> util = new ExcelUtil<IdwOrgStaff>(IdwOrgStaff.class);
        List<IdwOrgStaff> staffList = util.importExcel(SHEET_NAME, file.getInputStream());
        String operName = ShiroUtils.getUserName();
        String message = idwOrgStaffService.verifyImportOrgStaff(staffList);
        if (StringUtils.isBlank(message)) {
            idwOrgStaffService.importOrgStaff(staffList, updateSupport, operName);
            return AjaxResult.success("导入成功");
        } else {
            return AjaxResult.success(new StringBuffer().append("<br/>").append(message));
        }
    }*/

    /**
     * AI解析英文简介
     */
    @PostMapping("/parseEnglishBio")
    @ResponseBody
    public AjaxResult parseEnglishBio(String profileEn) {
        try {
            if (StringUtils.isBlank(profileEn)) {
                return AjaxResult.error("英文简介内容不能为空");
            }

            Map<String, Object> parsedData = idwPeopleMainService.parseEnglishBiography(profileEn);
            return AjaxResult.success("AI解析成功", parsedData);
        } catch (Exception e) {
            log.error("AI解析失败", e);
            return AjaxResult.error("AI解析失败：" + e.getMessage());
        }
    }

    /**
     * 从请求中提取IdwPeopleMain对象的参数
     */
    private IdwPeopleMain extractPeopleMainFromRequest(HttpServletRequest request) {
        IdwPeopleMain peopleMain = new IdwPeopleMain();

        String orgCode = request.getParameter("orgCode");
        String orgName = request.getParameter("orgName");

        String peopleCode = request.getParameter("peopleCode");
        String peopleId = request.getParameter("peopleId");

        // 基本信息
        String peopleNameCn = request.getParameter("peopleNameCn");
        String peopleNameEn = request.getParameter("peopleNameEn");
        String country = request.getParameter("country");
        String gender = request.getParameter("gender");
        String peopleTypeDetail = request.getParameter("peopleTypeDetail");
        String workStatus = request.getParameter("workStatus");

        // 详细信息
        String birthplace = request.getParameter("birthplace");
        String party = request.getParameter("party");
        String troopsCategory = request.getParameter("troopsCategory");
        String militaryRank = request.getParameter("militaryRank");
        String occupation = request.getParameter("occupation");
        String post = request.getParameter("post");

        // 日期信息
        String birthdayYear = request.getParameter("birthdayYear");
        String birthdayMonth = request.getParameter("birthdayMonth");
        String birthdayDay = request.getParameter("birthdayDay");
        String appointmentYear = request.getParameter("appointmentYear");
        String appointmentMonth = request.getParameter("appointmentMonth");
        String appointmentDay = request.getParameter("appointmentDay");
        String age = request.getParameter("age");

        // 教育信息
        String graduatedUniversity = request.getParameter("graduatedUniversity");
        String education = request.getParameter("education");

        // 联系信息
        String workplace = request.getParameter("workplace");
        String email = request.getParameter("email");
        String telephone = request.getParameter("telephone");

        // 经历信息
        String profileCn = request.getParameter("profileCn");
        String profileEn = request.getParameter("profileEn");
        String educationalExperience = request.getParameter("educationalExperience");
        String assignments = request.getParameter("assignments");

        // 其他信息
        String avatar = request.getParameter("avatar");
        String source = request.getParameter("source");

        // 设置基本信息
        if (StringUtils.isNotEmpty(peopleNameCn)) {
            peopleMain.setNameCn(peopleNameCn);
        }
        if (StringUtils.isNotEmpty(peopleNameEn)) {
            peopleMain.setNameEn(peopleNameEn);
        }
        if (StringUtils.isNotEmpty(country)) {
            peopleMain.setCountry(country);
        }
        if (StringUtils.isNotEmpty(gender)) {
            peopleMain.setGender(gender);
        }
        if (StringUtils.isNotEmpty(workStatus)) {
            peopleMain.setStatus(workStatus);
        }

        // 设置详细信息
        if (StringUtils.isNotEmpty(militaryRank)) {
            peopleMain.setMilitaryRank(militaryRank);
        }
        if (StringUtils.isNotEmpty(post)) {
            peopleMain.setPost(post);
        }
        if (StringUtils.isNotEmpty(orgName)) {
            peopleMain.setOrgName(orgName);
        }
        if (StringUtils.isNotEmpty(party)) {
            peopleMain.setParty(party);
        }
        if (StringUtils.isNotEmpty(troopsCategory)) {
            peopleMain.setTroopsCategory(troopsCategory);
        }
        if (StringUtils.isNotEmpty(occupation)) {
            peopleMain.setOccupation(occupation);
        }
        if (StringUtils.isNotEmpty(peopleTypeDetail)) {
            peopleMain.setPeopleType(peopleTypeDetail);
        }

        if (StringUtils.isNotBlank(birthplace)) {
            peopleMain.setBirthplace(birthplace);
        }

        // 处理出生日期
        if (StringUtils.isNotEmpty(birthdayYear) && StringUtils.isNotEmpty(birthdayMonth) && StringUtils.isNotEmpty(birthdayDay)) {
            String birthday = birthdayYear + "-" + String.format("%02d", Integer.parseInt(birthdayMonth)) + "-" + String.format("%02d", Integer.parseInt(birthdayDay));
            peopleMain.setBirthday(birthday);
            peopleMain.setBirthdayYear(birthdayYear);
            peopleMain.setBirthdayMonth(birthdayMonth);
            peopleMain.setBirthdayDay(birthdayDay);
        }

        if (StringUtils.isNotEmpty(age)) {
            try {
                peopleMain.setAge(Integer.parseInt(age));
            } catch (NumberFormatException e) {
                // 忽略年龄解析错误
            }
        }

        // 处理任职日期
        if (StringUtils.isNotEmpty(appointmentYear) && StringUtils.isNotEmpty(appointmentMonth) && StringUtils.isNotEmpty(appointmentDay)) {
            String appointmentDate = appointmentYear + "-" + String.format("%02d", Integer.parseInt(appointmentMonth)) + "-" + String.format("%02d", Integer.parseInt(appointmentDay));
            peopleMain.setAppointmentDate(appointmentDate);
            peopleMain.setAppointmentYear(appointmentYear);
            peopleMain.setAppointmentMonth(appointmentMonth);
            peopleMain.setAppointmentDay(appointmentDay);
        }

        // 设置教育信息
        if (StringUtils.isNotEmpty(graduatedUniversity)) {
            peopleMain.setGraduatedUniversity(graduatedUniversity);
        }
        if (StringUtils.isNotEmpty(education)) {
            peopleMain.setEducation(education);
        }

        // 设置联系信息
        if (StringUtils.isNotEmpty(workplace)) {
            peopleMain.setWorkplace(workplace);
        }
        if (StringUtils.isNotEmpty(email)) {
            peopleMain.setEmail(email);
        }
        if (StringUtils.isNotEmpty(telephone)) {
            peopleMain.setTelephone(telephone);
        }

        // 设置经历信息
        if (StringUtils.isNotEmpty(profileCn)) {
            peopleMain.setProfileCn(profileCn);
        }
        if (StringUtils.isNotEmpty(profileEn)) {
            peopleMain.setProfileEn(profileEn);
        }
        if (StringUtils.isNotEmpty(educationalExperience)) {
            peopleMain.setEducationalExperience(educationalExperience);
        }
        if (StringUtils.isNotEmpty(assignments)) {
            peopleMain.setAssignments(assignments);
        }

        // 设置其他信息
        if (StringUtils.isNotEmpty(avatar)) {
            peopleMain.setAvatar(avatar);
        }
        if (StringUtils.isNotEmpty(source)) {
            peopleMain.setSource(source);
        }

        return peopleMain;
    }

    /**
     * 导出机构人员列表
     */
    @RequiresPermissions("organization:staff:export")
    @Log(title = "机构人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(IdwOrgStaff idwOrgStaff) {
        List<IdwOrgStaff> list = idwOrgStaffService.selectByOrgCode(idwOrgStaff.getOrgCode());
        ExcelUtil<IdwOrgStaff> util = new ExcelUtil<IdwOrgStaff>(IdwOrgStaff.class);
        return util.exportExcel(list, SHEET_NAME);
    }

    /**
     * 生成缩略图
     *
     * @return 结果
     */
    @PostMapping("/generateThumbnail")
    @ResponseBody
    public AjaxResult generateThumbnail() {
        idwOrgStaffService.generateThumbnail();
        return AjaxResult.success();
    }
}
