<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增机构人员')"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-staff-add">
        <input type="hidden" name="orgCode" id="orgCode" th:value="${orgCode}">
        <div class="row">
            <div class="col-sm-8">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">人员编码：</label>
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="peopleCode" placeholder="可通过人员编码或名称搜索" name="peopleCode">
                                    <div class="input-group-btn">
                                        <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                            <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">中文名称：</label>
                            <div class="col-sm-9">
                                <input name="peopleNameCn" id="peopleNameCn" class="form-control" type="text" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">英文名称：</label>
                            <div class="col-sm-9">
                                <input name="peopleNameEn" id="peopleNameEn" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-4">
                <div class="form-group text-center">
                    <input type="hidden" name="avatar" id="avatar">
                    <p class="user-info-head" onclick="orgAvatar()">
                        <img class="img-lg" id="avatarUrl" th:src="@{'/img/default_people.png'}" th:onerror="'this.src=\'' + @{'/img/default_people.png'} + '\''">
                    </p>
                    <p><input type="file" id="orgAvatarInput" style="display: none;"></p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">状态：</label>
                    <div class="col-sm-8">
                        <div class="radio-box" th:each="dict : ${@dict.getType('sys_staff_status')}">
                            <input type="radio" th:id="${'status_' + dict.dictCode}" name="status"
                                   th:value="${dict.dictValue}" th:checked="${dict.default}" required>
                            <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">职位：</label>
                    <div class="col-sm-8">
                        <input name="position" id="position" class="form-control" type="text">
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">上级人员：</label>
                    <div class="col-sm-8">
                        <select class="form-control" name="managerStaffId" id="managerStaffId">
                            <option value="">请选择上级人员</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-sm-6" th:if="${orgType != '作战指挥' && orgType != '军事基地'}">
                <div class="form-group">
                    <label class="col-sm-4 control-label">人员分类：</label>
                    <div class="col-sm-8">
                        <input name="peopleType" id="peopleType" class="form-control" type="text">
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">中文简介：</label>
            <div class="col-sm-10">
                <textarea name="profileCn" id="profileCn" class="form-control" rows="6"></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">英文简介：</label>
            <div class="col-sm-10">
                <textarea name="profileEn" id="profileEn" class="form-control" rows="6"></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label is-required">数据来源：</label>
            <div class="col-sm-10">
                <input name="source" id="source" class="form-control" required>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    let people = ctx + "people/people"
    let prefix = ctx + "organization/staff"
    $("#form-staff-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/addStaff", $('#form-staff-add').serialize());
        }
    }

    //加载上级人员下拉框 managerStaffId
    let managerStaffId = document.getElementById('managerStaffId');
    let orgStaffList = [[${orgStaffList}]];
    for (let i = 0; i < orgStaffList.length; i++) {
        let nameEn = '';
        if (orgStaffList[i].peopleNameEn != '' && orgStaffList[i].peopleNameEn != null) {
            nameEn = '（' + orgStaffList[i].peopleNameEn + '）';
        }
        managerStaffId.options.add(new Option(orgStaffList[i].peopleNameCn + nameEn, orgStaffList[i].staffId));
    }

    let orgType = [[${orgType}]]
    //加载人员搜索下拉框 人员编码 peopleCode
    var peopleBsSuggest = $("#peopleCode").bsSuggest({
        url: people + "/selectPeopleMainListByKeyword?keyword=",
        //ignorecase : true,//搜索忽略大小写
        getDataMethod: 'url',//获取数据的方式，总是从 URL 获取
        autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
        listStyle: {
            'padding-top': 0,
            'max-height': '375px',
            'max-width': '650px',
            'overflow': 'auto',
            'width': 'auto',
            'transition': '0.3s',
            '-webkit-transition': '0.3s',
            '-moz-transition': '0.3s',
            '-o-transition': '0.3s'
        },//列表的样式控制
        idField: "peopleCode",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
        keyField: "peopleCode",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
        effectiveFields: ['peopleCode', 'nameCn', 'nameEn', 'orgName', 'post'],//设置展示字段
        effectiveFieldsAlias: {
            peopleCode: '人员编码',
            nameCn: '中文名称',
            nameEn: '英文名称',
            orgName: '所在机构名称',
            post: '职位'
        }//设置字段别名
    }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        //选择后隐藏下拉框
        $("#peopleCode").bsSuggest("hide");
        $('#peopleNameCn').val(result.nameCn);
        $('#peopleNameEn').val(result.nameEn);
        if (orgType != '作战指挥' && orgType != '军事基地') {
            $('#peopleType').val(result.category);
        }
        if (result.avatar == '' || result.avatar == null) {
            $("#avatarUrl").attr("src", '/img/default_people.png');
            $("#avatar").val(result.avatar);
        } else {
            $("#avatarUrl").attr("src", result.avatar);
            $("#avatar").val(result.avatar);
        }
        $("#position").val(result.post);
        $("#profileCn").val(result.profileCn);
        $("#profileEn").val(result.profileEn);
    }).on('onUnsetSelectValue', function (e) {//当设置了 idField，且自由输入内容时触发（与背景警告色显示同步）
        //console.log("onUnsetSelectValue");
    }).on('onHideDropdown', function (e, data) { //选中后或输入框失去焦点时，下拉框隐藏时触发
        //console.log('onHideDropdown', e.target.value, data);
        //赋值人员其他数据
    }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
        let background = $('#peopleCode').css("background-color");
        if (background == 'rgba(255, 0, 0, 0.1)'){
            $("#peopleCode").val('');
            $('#peopleCode').css("background-color", 'rgb(255, 255, 255)')
        }
    });

    //机构人员头像
    function orgAvatar() {
        let peopleCode = $("#peopleCode").val();
        if (peopleCode == '' || peopleCode == null){
            $('#orgAvatarInput').trigger('click');
        }
    }

    $("#orgAvatarInput").change(function () {
        var data = new FormData();
        data.append("file", $("#orgAvatarInput")[0].files[0]);
        $.ajax({
            type: "POST",
            url: ctx + "common/upload/img",
            data: data,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $("#avatarUrl").attr("src", result.url)
                    $("#avatar").val(result.url)
                }
            },
            error: function (error) {
                alert("图片上传失败。");
            }
        });
    });
</script>
</body>
</html>