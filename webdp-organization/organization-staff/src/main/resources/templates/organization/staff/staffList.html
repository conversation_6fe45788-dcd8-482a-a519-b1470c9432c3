<div class="container-div">
    <input type="hidden" value="机构人员列表">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-staff">
                <input type="hidden" name="orgCode" th:value="${orgCode}">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>人员名称：</label>
                            <input type="text" name="peopleNameCn"/>
                        </li>
                        <li>
                            <label>职位：</label>
                            <input type="text" name="position"/>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="status" th:with="type=${@dict.getType('sys_staff_status')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-staff', 'bootstrap-table-staff')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-staff', 'bootstrap-table-staff')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-staff" role="group">
            <a class="btn btn-success" onclick="$.operate.add(null, 950, 750)" shiro:hasPermission="organization:staff:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit(null, 950, 750)" shiro:hasPermission="organization:staff:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="organization:staff:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-staff"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('organization:staff:edit')}]];
    let removeFlag = [[${@permission.hasPermi('organization:staff:remove')}]];
    let statusDatas = [[${@dict.getType('sys_staff_status')}]];
    let prefix = ctx + "organization/staff";

    $(function () {
        let options = {
            id: "bootstrap-table-staff",          // 指定表格ID
            toolbar: "toolbar-staff",   // 指定工具栏ID
            formId: "form-staff",
            url: prefix + "/staffList",
            createUrl: prefix + "/addStaff/" + orgCode,
            updateUrl: prefix + "/editStaff/{id}",
            removeUrl: prefix + "/removeStaff",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "机构人员",
            uniqueId: "staffId",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'avatar',
                    title: '头像',
                    width: 80,
                    formatter: function (value, row, index) {
                        if (value != null && value != '') {
                            return $.table.imageView(value, 300, 300);
                        } else {
                            return $.table.imageView('/img/default_people.png', 300, 300);
                        }
                    }
                },
                {
                    field: 'peopleNameCn',
                    title: '中文名称'
                },
                {
                    field: 'peopleNameEn',
                    title: '英文名称'
                },
                {
                    field: 'position',
                    title: '职位'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'updateTime',
                    title: '修改时间'
                },
                {
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            var sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0) {
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);//根据正则取出网址域名
                                    if (domainName != null && domainName != '') {
                                        if (i > 0) {
                                            html += '</br>'
                                        }
                                        html += "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != '') {
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.staffId + '\', 950, 750)"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.staffId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>